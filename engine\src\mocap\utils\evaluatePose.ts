/**
 * 评估动作捕捉姿势
 * 检测和识别各种姿势
 */
import * as THREE from 'three';
import type { Entity } from '../../core/Entity';
import { MotionCaptureComponent } from '../components/MotionCaptureComponent';
import { MotionCapturePoseComponent, MotionCapturePoseType } from '../components/MotionCapturePoseComponent';
import { LandmarkIndices } from '../constants/LandmarkIndices';
import { WorldLandmarkData } from '../types/LandmarkData';
import { Debug } from '../../utils/Debug';

// 姿势角度阈值（弧度）
const SITTING_ANGLE_THRESHOLD = 1.25;
const TPOSE_ANGLE_THRESHOLD = 0.3;
const APOSE_ANGLE_THRESHOLD = 0.6;

/**
 * 评估姿势
 * @param entity 实体
 * @param poseComponent 姿势组件
 */
export function evaluatePose(entity: Entity, poseComponent: MotionCapturePoseComponent): void {
  // 获取动作捕捉组件
  const motionCaptureComponent = entity.getComponent<MotionCaptureComponent>(MotionCaptureComponent.TYPE);
  if (!motionCaptureComponent) {
    Debug.warn('evaluatePose', `Entity ${entity.id} does not have MotionCaptureComponent`);
    return;
  }

  // 如果没有解算下半身，无法评估姿势
  if (!motionCaptureComponent.solvingLowerBody) {
    return;
  }

  // 获取关键点数据
  const worldLandmarks = motionCaptureComponent.worldLandmarks;
  if (!worldLandmarks || worldLandmarks.length === 0) {
    return;
  }

  // 更新姿势持续时间
  const deltaTime = 1 / 60; // 假设60帧每秒
  poseComponent.updatePoseDurations(deltaTime);

  // 评估各种姿势
  evaluateSittingPose(worldLandmarks, poseComponent);
  evaluateTPose(worldLandmarks, poseComponent);
  evaluateAPose(worldLandmarks, poseComponent);
  evaluateWalkingPose(worldLandmarks, poseComponent);
  evaluateRunningPose(worldLandmarks, poseComponent);
  evaluateJumpingPose(worldLandmarks, poseComponent);
  evaluateCrouchingPose(worldLandmarks, poseComponent);
  evaluatePronePose(worldLandmarks, poseComponent);
}

/**
 * 评估坐姿
 * @param landmarks 关键点数据
 * @param poseComponent 姿势组件
 */
function evaluateSittingPose(landmarks: WorldLandmarkData[], poseComponent: MotionCapturePoseComponent): void {
  // 获取关键点
  const leftHip = landmarks[LandmarkIndices.LEFT_HIP];
  const rightHip = landmarks[LandmarkIndices.RIGHT_HIP];
  const leftKnee = landmarks[LandmarkIndices.LEFT_KNEE];
  const rightKnee = landmarks[LandmarkIndices.RIGHT_KNEE];

  // 检查关键点是否存在
  if (!leftHip || !rightHip || !leftKnee || !rightKnee) {
    return;
  }

  // 计算髋关节到膝盖的向量
  const leftHipToKnee = new THREE.Vector3(
    leftKnee.x - leftHip.x,
    leftKnee.y - leftHip.y,
    leftKnee.z - leftHip.z
  ).normalize();

  const rightHipToKnee = new THREE.Vector3(
    rightKnee.x - rightHip.x,
    rightKnee.y - rightHip.y,
    rightKnee.z - rightHip.z
  ).normalize();

  // 计算向下的向量
  const downVector = new THREE.Vector3(0, 1, 0);

  // 计算角度
  const leftAngle = Math.acos(leftHipToKnee.dot(downVector));
  const rightAngle = Math.acos(rightHipToKnee.dot(downVector));

  // 计算平均角度
  const avgAngle = (leftAngle + rightAngle) / 2;

  // 检查是否是坐姿
  const isSitting = avgAngle > SITTING_ANGLE_THRESHOLD;

  // 更新姿势状态
  poseComponent.setPoseState(MotionCapturePoseType.SITTING, {
    begun: isSitting,
    intensity: isSitting ? Math.min(1, (avgAngle - SITTING_ANGLE_THRESHOLD) / (Math.PI / 2 - SITTING_ANGLE_THRESHOLD)) : 0,
    confidence: isSitting ? 0.8 : 0
  });

  // 如果是坐姿，更新站立状态
  if (isSitting) {
    poseComponent.setPoseState(MotionCapturePoseType.STANDING, {
      begun: false,
      intensity: 0,
      confidence: 0
    });
  } else {
    poseComponent.setPoseState(MotionCapturePoseType.STANDING, {
      begun: true,
      intensity: 1,
      confidence: 0.9
    });
  }
}

/**
 * 评估T姿势
 * @param landmarks 关键点数据
 * @param poseComponent 姿势组件
 */
function evaluateTPose(landmarks: WorldLandmarkData[], poseComponent: MotionCapturePoseComponent): void {
  // 获取关键点
  const leftShoulder = landmarks[LandmarkIndices.LEFT_SHOULDER];
  const rightShoulder = landmarks[LandmarkIndices.RIGHT_SHOULDER];
  const leftElbow = landmarks[LandmarkIndices.LEFT_ELBOW];
  const rightElbow = landmarks[LandmarkIndices.RIGHT_ELBOW];
  const leftWrist = landmarks[LandmarkIndices.LEFT_WRIST];
  const rightWrist = landmarks[LandmarkIndices.RIGHT_WRIST];

  // 检查关键点是否存在
  if (!leftShoulder || !rightShoulder || !leftElbow || !rightElbow || !leftWrist || !rightWrist) {
    return;
  }

  // 计算肩膀到肘部的向量
  const leftShoulderToElbow = new THREE.Vector3(
    leftElbow.x - leftShoulder.x,
    leftElbow.y - leftShoulder.y,
    leftElbow.z - leftShoulder.z
  ).normalize();

  const rightShoulderToElbow = new THREE.Vector3(
    rightElbow.x - rightShoulder.x,
    rightElbow.y - rightShoulder.y,
    rightElbow.z - rightShoulder.z
  ).normalize();

  // 计算水平向量
  const leftHorizontal = new THREE.Vector3(-1, 0, 0);
  const rightHorizontal = new THREE.Vector3(1, 0, 0);

  // 计算角度
  const leftAngle = Math.acos(leftShoulderToElbow.dot(leftHorizontal));
  const rightAngle = Math.acos(rightShoulderToElbow.dot(rightHorizontal));

  // 计算肘部到手腕的向量
  const leftElbowToWrist = new THREE.Vector3(
    leftWrist.x - leftElbow.x,
    leftWrist.y - leftElbow.y,
    leftWrist.z - leftElbow.z
  ).normalize();

  const rightElbowToWrist = new THREE.Vector3(
    rightWrist.x - rightElbow.x,
    rightWrist.y - rightElbow.y,
    rightWrist.z - rightElbow.z
  ).normalize();

  // 计算前臂角度
  const leftForearmAngle = Math.acos(leftElbowToWrist.dot(leftHorizontal));
  const rightForearmAngle = Math.acos(rightElbowToWrist.dot(rightHorizontal));

  // 检查是否是T姿势
  const isTPose =
    leftAngle < TPOSE_ANGLE_THRESHOLD &&
    rightAngle < TPOSE_ANGLE_THRESHOLD &&
    leftForearmAngle < TPOSE_ANGLE_THRESHOLD &&
    rightForearmAngle < TPOSE_ANGLE_THRESHOLD;

  // 更新姿势状态
  poseComponent.setPoseState(MotionCapturePoseType.T_POSE, {
    begun: isTPose,
    intensity: isTPose ? 1 : 0,
    confidence: isTPose ? 0.9 : 0
  });
}

/**
 * 评估A姿势
 * @param landmarks 关键点数据
 * @param poseComponent 姿势组件
 */
function evaluateAPose(landmarks: WorldLandmarkData[], poseComponent: MotionCapturePoseComponent): void {
  // 获取关键点
  const leftShoulder = landmarks[LandmarkIndices.LEFT_SHOULDER];
  const rightShoulder = landmarks[LandmarkIndices.RIGHT_SHOULDER];
  const leftElbow = landmarks[LandmarkIndices.LEFT_ELBOW];
  const rightElbow = landmarks[LandmarkIndices.RIGHT_ELBOW];
  const leftWrist = landmarks[LandmarkIndices.LEFT_WRIST];
  const rightWrist = landmarks[LandmarkIndices.RIGHT_WRIST];

  // 检查关键点是否存在
  if (!leftShoulder || !rightShoulder || !leftElbow || !rightElbow || !leftWrist || !rightWrist) {
    return;
  }

  // 计算肩膀到肘部的向量
  const leftShoulderToElbow = new THREE.Vector3(
    leftElbow.x - leftShoulder.x,
    leftElbow.y - leftShoulder.y,
    leftElbow.z - leftShoulder.z
  ).normalize();

  const rightShoulderToElbow = new THREE.Vector3(
    rightElbow.x - rightShoulder.x,
    rightElbow.y - rightShoulder.y,
    rightElbow.z - rightShoulder.z
  ).normalize();

  // 计算A姿势向量（向下45度）
  const leftAVector = new THREE.Vector3(-0.7071, -0.7071, 0);
  const rightAVector = new THREE.Vector3(0.7071, -0.7071, 0);

  // 计算角度
  const leftAngle = Math.acos(leftShoulderToElbow.dot(leftAVector));
  const rightAngle = Math.acos(rightShoulderToElbow.dot(rightAVector));

  // 检查是否是A姿势
  const isAPose =
    leftAngle < APOSE_ANGLE_THRESHOLD &&
    rightAngle < APOSE_ANGLE_THRESHOLD;

  // 更新姿势状态
  poseComponent.setPoseState(MotionCapturePoseType.A_POSE, {
    begun: isAPose,
    intensity: isAPose ? 1 : 0,
    confidence: isAPose ? 0.8 : 0
  });
}

/**
 * 评估行走姿势
 * @param landmarks 关键点数据
 * @param poseComponent 姿势组件
 */
function evaluateWalkingPose(landmarks: WorldLandmarkData[], poseComponent: MotionCapturePoseComponent): void {
  // 获取关键点
  const leftAnkle = landmarks[LandmarkIndices.LEFT_ANKLE];
  const rightAnkle = landmarks[LandmarkIndices.RIGHT_ANKLE];
  const leftKnee = landmarks[LandmarkIndices.LEFT_KNEE];
  const rightKnee = landmarks[LandmarkIndices.RIGHT_KNEE];

  // 检查关键点是否存在
  if (!leftAnkle || !rightAnkle || !leftKnee || !rightKnee) {
    return;
  }

  // 计算脚踝之间的距离
  const ankleDistance = new THREE.Vector3(
    rightAnkle.x - leftAnkle.x,
    rightAnkle.y - leftAnkle.y,
    rightAnkle.z - leftAnkle.z
  ).length();

  // 计算膝盖之间的距离
  const kneeDistance = new THREE.Vector3(
    rightKnee.x - leftKnee.x,
    rightKnee.y - leftKnee.y,
    rightKnee.z - leftKnee.z
  ).length();

  // 检查是否是行走姿势（脚踝距离大于膝盖距离的1.2倍）
  const isWalking = ankleDistance > kneeDistance * 1.2;

  // 更新姿势状态
  poseComponent.setPoseState(MotionCapturePoseType.WALKING, {
    begun: isWalking,
    intensity: isWalking ? Math.min(1, (ankleDistance / kneeDistance - 1.2) / 0.8) : 0,
    confidence: isWalking ? 0.7 : 0
  });
}

/**
 * 评估跑步姿势
 * @param landmarks 关键点数据
 * @param poseComponent 姿势组件
 */
function evaluateRunningPose(landmarks: WorldLandmarkData[], poseComponent: MotionCapturePoseComponent): void {
  // 获取关键点
  const leftAnkle = landmarks[LandmarkIndices.LEFT_ANKLE];
  const rightAnkle = landmarks[LandmarkIndices.RIGHT_ANKLE];
  const leftKnee = landmarks[LandmarkIndices.LEFT_KNEE];
  const rightKnee = landmarks[LandmarkIndices.RIGHT_KNEE];

  // 检查关键点是否存在
  if (!leftAnkle || !rightAnkle || !leftKnee || !rightKnee) {
    return;
  }

  // 计算脚踝之间的距离
  const ankleDistance = new THREE.Vector3(
    rightAnkle.x - leftAnkle.x,
    rightAnkle.y - leftAnkle.y,
    rightAnkle.z - leftAnkle.z
  ).length();

  // 计算膝盖之间的距离
  const kneeDistance = new THREE.Vector3(
    rightKnee.x - leftKnee.x,
    rightKnee.y - leftKnee.y,
    rightKnee.z - leftKnee.z
  ).length();

  // 检查是否是跑步姿势（脚踝距离大于膝盖距离的1.5倍）
  const isRunning = ankleDistance > kneeDistance * 1.5;

  // 更新姿势状态
  poseComponent.setPoseState(MotionCapturePoseType.RUNNING, {
    begun: isRunning,
    intensity: isRunning ? Math.min(1, (ankleDistance / kneeDistance - 1.5) / 1.0) : 0,
    confidence: isRunning ? 0.7 : 0
  });
}

/**
 * 评估跳跃姿势
 * @param landmarks 关键点数据
 * @param poseComponent 姿势组件
 */
function evaluateJumpingPose(landmarks: WorldLandmarkData[], poseComponent: MotionCapturePoseComponent): void {
  // 获取关键点
  const leftAnkle = landmarks[LandmarkIndices.LEFT_ANKLE];
  const rightAnkle = landmarks[LandmarkIndices.RIGHT_ANKLE];
  const leftHip = landmarks[LandmarkIndices.LEFT_HIP];
  const rightHip = landmarks[LandmarkIndices.RIGHT_HIP];

  // 检查关键点是否存在
  if (!leftAnkle || !rightAnkle || !leftHip || !rightHip) {
    return;
  }

  // 计算髋关节到脚踝的距离
  const leftHipToAnkle = new THREE.Vector3(
    leftAnkle.x - leftHip.x,
    leftAnkle.y - leftHip.y,
    leftAnkle.z - leftHip.z
  ).length();

  const rightHipToAnkle = new THREE.Vector3(
    rightAnkle.x - rightHip.x,
    rightAnkle.y - rightHip.y,
    rightAnkle.z - rightHip.z
  ).length();

  // 计算平均距离
  const avgDistance = (leftHipToAnkle + rightHipToAnkle) / 2;

  // 检查是否是跳跃姿势（髋关节到脚踝的距离小于正常值的0.8倍）
  const isJumping = avgDistance < 0.8;

  // 更新姿势状态
  poseComponent.setPoseState(MotionCapturePoseType.JUMPING, {
    begun: isJumping,
    intensity: isJumping ? Math.min(1, (0.8 - avgDistance) / 0.3) : 0,
    confidence: isJumping ? 0.6 : 0
  });
}

/**
 * 评估蹲下姿势
 * @param landmarks 关键点数据
 * @param poseComponent 姿势组件
 */
function evaluateCrouchingPose(landmarks: WorldLandmarkData[], poseComponent: MotionCapturePoseComponent): void {
  // 获取关键点
  const leftHip = landmarks[LandmarkIndices.LEFT_HIP];
  const rightHip = landmarks[LandmarkIndices.RIGHT_HIP];
  const leftKnee = landmarks[LandmarkIndices.LEFT_KNEE];
  const rightKnee = landmarks[LandmarkIndices.RIGHT_KNEE];
  const leftAnkle = landmarks[LandmarkIndices.LEFT_ANKLE];
  const rightAnkle = landmarks[LandmarkIndices.RIGHT_ANKLE];

  // 检查关键点是否存在
  if (!leftHip || !rightHip || !leftKnee || !rightKnee || !leftAnkle || !rightAnkle) {
    return;
  }

  // 计算髋关节到膝盖的向量
  const leftHipToKnee = new THREE.Vector3(
    leftKnee.x - leftHip.x,
    leftKnee.y - leftHip.y,
    leftKnee.z - leftHip.z
  ).normalize();

  const rightHipToKnee = new THREE.Vector3(
    rightKnee.x - rightHip.x,
    rightKnee.y - rightHip.y,
    rightKnee.z - rightHip.z
  ).normalize();

  // 计算膝盖到脚踝的向量
  const leftKneeToAnkle = new THREE.Vector3(
    leftAnkle.x - leftKnee.x,
    leftAnkle.y - leftKnee.y,
    leftAnkle.z - leftKnee.z
  ).normalize();

  const rightKneeToAnkle = new THREE.Vector3(
    rightAnkle.x - rightKnee.x,
    rightAnkle.y - rightKnee.y,
    rightAnkle.z - rightKnee.z
  ).normalize();

  // 计算角度
  const leftAngle = Math.acos(leftHipToKnee.dot(leftKneeToAnkle));
  const rightAngle = Math.acos(rightHipToKnee.dot(rightKneeToAnkle));

  // 计算平均角度
  const avgAngle = (leftAngle + rightAngle) / 2;

  // 检查是否是蹲下姿势（膝盖弯曲角度小于90度）
  const isCrouching = avgAngle < Math.PI / 2;

  // 更新姿势状态
  poseComponent.setPoseState(MotionCapturePoseType.CROUCHING, {
    begun: isCrouching,
    intensity: isCrouching ? Math.min(1, (Math.PI / 2 - avgAngle) / (Math.PI / 4)) : 0,
    confidence: isCrouching ? 0.8 : 0
  });
}

/**
 * 评估趴下姿势
 * @param landmarks 关键点数据
 * @param poseComponent 姿势组件
 */
function evaluatePronePose(landmarks: WorldLandmarkData[], poseComponent: MotionCapturePoseComponent): void {
  // 获取关键点
  const leftShoulder = landmarks[LandmarkIndices.LEFT_SHOULDER];
  const rightShoulder = landmarks[LandmarkIndices.RIGHT_SHOULDER];
  const leftHip = landmarks[LandmarkIndices.LEFT_HIP];
  const rightHip = landmarks[LandmarkIndices.RIGHT_HIP];

  // 检查关键点是否存在
  if (!leftShoulder || !rightShoulder || !leftHip || !rightHip) {
    return;
  }

  // 计算肩膀中点
  const shoulderCenter = new THREE.Vector3(
    (leftShoulder.x + rightShoulder.x) / 2,
    (leftShoulder.y + rightShoulder.y) / 2,
    (leftShoulder.z + rightShoulder.z) / 2
  );

  // 计算髋关节中点
  const hipCenter = new THREE.Vector3(
    (leftHip.x + rightHip.x) / 2,
    (leftHip.y + rightHip.y) / 2,
    (leftHip.z + rightHip.z) / 2
  );

  // 计算躯干向量
  const torsoVector = new THREE.Vector3(
    shoulderCenter.x - hipCenter.x,
    shoulderCenter.y - hipCenter.y,
    shoulderCenter.z - hipCenter.z
  ).normalize();

  // 计算向前的向量
  const forwardVector = new THREE.Vector3(0, 0, -1);

  // 计算角度
  const angle = Math.acos(torsoVector.dot(forwardVector));

  // 检查是否是趴下姿势（躯干几乎水平）
  const isProne = angle < Math.PI / 4;

  // 更新姿势状态
  poseComponent.setPoseState(MotionCapturePoseType.PRONE, {
    begun: isProne,
    intensity: isProne ? Math.min(1, (Math.PI / 4 - angle) / (Math.PI / 8)) : 0,
    confidence: isProne ? 0.7 : 0
  });
}
